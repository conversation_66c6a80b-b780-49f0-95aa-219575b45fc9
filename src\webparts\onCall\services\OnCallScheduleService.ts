import { SPHttpClient, SPHttpClientResponse } from '@microsoft/sp-http';
import { IOnCallScheduleItem, IOnCallScheduleResponse } from '../components/IOnCallScheduleItem';

export class OnCallScheduleService {
  private spHttpClient: SPHttpClient;
  private webUrl: string;
  private listName: string;

  constructor(spHttpClient: SPHttpClient, webUrl: string, listName: string) {
    this.spHttpClient = spHttpClient;
    this.webUrl = webUrl;
    this.listName = listName || 'On Call Schedule';
  }

  /**
   * Get OnCall schedule items filtered by date range
   * @param startDate - Start date for filtering (optional)
   * @param endDate - End date for filtering (optional)
   * @returns Promise with filtered OnCall schedule items
   */
  public async getOnCallScheduleItems(startDate?: Date, endDate?: Date): Promise<IOnCallScheduleItem[]> {
    try {
      let filterQuery = '';
      
      if (startDate && endDate) {
        const startDateISO = startDate.toISOString();
        const endDateISO = endDate.toISOString();
        
        // Filter items where the schedule overlaps with the selected date range
        filterQuery = `$filter=(StartDate le datetime'${endDateISO}') and (EndDate ge datetime'${startDateISO}')`;
      } else if (startDate) {
        const startDateISO = startDate.toISOString();
        filterQuery = `$filter=EndDate ge datetime'${startDateISO}'`;
      } else if (endDate) {
        const endDateISO = endDate.toISOString();
        filterQuery = `$filter=StartDate le datetime'${endDateISO}'`;
      }

      const selectFields = '$select=Id,Title,StartDate,EndDate,Manager/Title,Manager/EMail,Manager/Id,ManagerMobile,QueueManager/Title,QueueManager/EMail,QueueManager/Id,QueueManagerMobile,PrimaryContact/Title,PrimaryContact/EMail,PrimaryContact/Id,PrimaryContactMobile,Comments,Created,Modified';
      const expandFields = '$expand=Manager,QueueManager,PrimaryContact';
      const orderBy = '$orderby=StartDate asc';
      
      let queryString = `${selectFields}&${expandFields}&${orderBy}`;
      if (filterQuery) {
        queryString += `&${filterQuery}`;
      }

      const encodedListName = encodeURIComponent(this.listName);
      const restUrl = `${this.webUrl}/_api/web/lists/getbytitle('${encodedListName}')/items?${queryString}`;

      const response: SPHttpClientResponse = await this.spHttpClient.get(
        restUrl,
        SPHttpClient.configurations.v1
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: IOnCallScheduleResponse = await response.json();
      return data.value || [];
    } catch (error) {
      console.error('Error fetching OnCall schedule items:', error);
      throw error;
    }
  }

  /**
   * Get all OnCall schedule items (no filtering)
   * @returns Promise with all OnCall schedule items
   */
  public async getAllOnCallScheduleItems(): Promise<IOnCallScheduleItem[]> {
    return this.getOnCallScheduleItems();
  }

  /**
   * Format date for display
   * @param dateString - ISO date string
   * @returns Formatted date string
   */
  public static formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * Format date for display (date only)
   * @param dateString - ISO date string
   * @returns Formatted date string
   */
  public static formatDateOnly(dateString: string): string {
    if (!dateString) {
      return '';
    }

    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return '';
    }

    // Check if the date is a reasonable date (not 1969 or other invalid dates)
    if (date.getFullYear() < 1970) {
      return '';
    }

    return date.toLocaleDateString();
  }

  /**
   * Create a new OnCall schedule item
   * @param item - The item data to create
   * @returns Promise with the created item
   */
  public async createOnCallScheduleItem(item: Partial<IOnCallScheduleItem>): Promise<IOnCallScheduleItem> {
    try {
      const encodedListName = encodeURIComponent(this.listName);
      const restUrl = `${this.webUrl}/_api/web/lists/getbytitle('${encodedListName}')/items`;

      const body = this.buildItemBody(item);

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        restUrl,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=verbose',
            'Content-Type': 'application/json;odata=verbose',
            'odata-version': ''
          },
          body: JSON.stringify(body)
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      return this.mapResponseToItem(data.d);
    } catch (error) {
      console.error('Error creating OnCall schedule item:', error);
      throw error;
    }
  }

  /**
   * Update an existing OnCall schedule item
   * @param itemId - The ID of the item to update
   * @param item - The updated item data
   * @returns Promise with the updated item
   */
  public async updateOnCallScheduleItem(itemId: number, item: Partial<IOnCallScheduleItem>): Promise<IOnCallScheduleItem> {
    try {
      const encodedListName = encodeURIComponent(this.listName);
      const restUrl = `${this.webUrl}/_api/web/lists/getbytitle('${encodedListName}')/items(${itemId})`;

      const body = this.buildItemBody(item);

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        restUrl,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=verbose',
            'Content-Type': 'application/json;odata=verbose',
            'odata-version': '',
            'IF-MATCH': '*',
            'X-HTTP-Method': 'MERGE'
          },
          body: JSON.stringify(body)
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      // Get the updated item
      return this.getOnCallScheduleItemById(itemId);
    } catch (error) {
      console.error('Error updating OnCall schedule item:', error);
      throw error;
    }
  }

  /**
   * Delete an OnCall schedule item
   * @param itemId - The ID of the item to delete
   * @returns Promise that resolves when the item is deleted
   */
  public async deleteOnCallScheduleItem(itemId: number): Promise<void> {
    try {
      const encodedListName = encodeURIComponent(this.listName);
      const restUrl = `${this.webUrl}/_api/web/lists/getbytitle('${encodedListName}')/items(${itemId})`;

      const response: SPHttpClientResponse = await this.spHttpClient.post(
        restUrl,
        SPHttpClient.configurations.v1,
        {
          headers: {
            'Accept': 'application/json;odata=verbose',
            'Content-Type': 'application/json;odata=verbose',
            'odata-version': '',
            'IF-MATCH': '*',
            'X-HTTP-Method': 'DELETE'
          }
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }
    } catch (error) {
      console.error('Error deleting OnCall schedule item:', error);
      throw error;
    }
  }

  /**
   * Get a single OnCall schedule item by ID
   * @param itemId - The ID of the item to retrieve
   * @returns Promise with the item
   */
  public async getOnCallScheduleItemById(itemId: number): Promise<IOnCallScheduleItem> {
    try {
      const encodedListName = encodeURIComponent(this.listName);
      const selectFields = '$select=Id,Title,StartDate,EndDate,Manager/Title,Manager/EMail,Manager/Id,ManagerMobile,QueueManager/Title,QueueManager/EMail,QueueManager/Id,QueueManagerMobile,PrimaryContact/Title,PrimaryContact/EMail,PrimaryContact/Id,PrimaryContactMobile,Comments,Created,Modified';
      const expandFields = '$expand=Manager,QueueManager,PrimaryContact';
      const restUrl = `${this.webUrl}/_api/web/lists/getbytitle('${encodedListName}')/items(${itemId})?${selectFields}&${expandFields}`;

      const response: SPHttpClientResponse = await this.spHttpClient.get(
        restUrl,
        SPHttpClient.configurations.v1
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return this.mapResponseToItem(data);
    } catch (error) {
      console.error('Error fetching OnCall schedule item by ID:', error);
      throw error;
    }
  }

  /**
   * Build the request body for create/update operations
   * @param item - The item data
   * @returns The formatted request body
   */
  private buildItemBody(item: Partial<IOnCallScheduleItem>): any {
    const body: any = {};

    if (item.Title !== undefined) {
      body.Title = item.Title;
    }

    if (item.StartDate !== undefined) {
      body.StartDate = item.StartDate;
    }

    if (item.EndDate !== undefined) {
      body.EndDate = item.EndDate;
    }

    if (item.ManagerMobile !== undefined) {
      body.ManagerMobile = item.ManagerMobile;
    }

    if (item.QueueManagerMobile !== undefined) {
      body.QueueManagerMobile = item.QueueManagerMobile;
    }

    if (item.PrimaryContactMobile !== undefined) {
      body.PrimaryContactMobile = item.PrimaryContactMobile;
    }

    if (item.Comments !== undefined) {
      body.Comments = item.Comments;
    }

    // Handle person fields
    if (item.Manager !== undefined) {
      body.ManagerId = item.Manager?.Id || null;
    }

    if (item.QueueManager !== undefined) {
      body.QueueManagerId = item.QueueManager?.Id || null;
    }

    if (item.PrimaryContact !== undefined) {
      body.PrimaryContactId = item.PrimaryContact?.Id || null;
    }

    return body;
  }

  /**
   * Map SharePoint response to IOnCallScheduleItem
   * @param data - The raw SharePoint response data
   * @returns Mapped item
   */
  private mapResponseToItem(data: any): IOnCallScheduleItem {
    return {
      Id: data.Id,
      Title: data.Title || '',
      Manager: data.Manager ? {
        Title: data.Manager.Title,
        EMail: data.Manager.EMail,
        Id: data.Manager.Id
      } : undefined,
      ManagerMobile: data.ManagerMobile || '',
      QueueManager: data.QueueManager ? {
        Title: data.QueueManager.Title,
        EMail: data.QueueManager.EMail,
        Id: data.QueueManager.Id
      } : undefined,
      QueueManagerMobile: data.QueueManagerMobile || '',
      PrimaryContact: data.PrimaryContact ? {
        Title: data.PrimaryContact.Title,
        EMail: data.PrimaryContact.EMail,
        Id: data.PrimaryContact.Id
      } : undefined,
      PrimaryContactMobile: data.PrimaryContactMobile || '',
      StartDate: data.StartDate,
      EndDate: data.EndDate,
      Comments: data.Comments || '',
      Created: data.Created,
      Modified: data.Modified
    };
  }
}
